/* eslint-disable max-lines */
import {MCPApplication} from '@/types/mcp/application';
import {
    ApiDefinition,
    AppListForMCPServerBase,
    BaseParam,
    MCPServerBase,
    MCPServerParam,
    MCPServerStatus,
    MCPSpace,
    MCPSpaceMember,
    MCPToolItem,
    SpaceLabel,
    MCPServerAnalysisData,
    MCPToolLogItem,
    MCPToolLogListParams,
} from '@/types/mcp/mcp';
import {createMCPInterface} from '@/utils/createInterface/mcp';
import {PaginationResponse} from './type';
// import {MCP_APPLICATION, MCP_SERVER_TOOL_LIST} from './mock';

export interface GetMCPWorkspacesParam {
    mine: boolean;
    keywords?: string;
    pn?: number;
    size?: number;
}

export interface WorkspaceIdParam {
    workspaceId: number;
}

export interface ApplicationIdParam {
    applicationId: number;
}

/**
 * 获取空间列表
 */
export const apiGetMCPWorkspaces = createMCPInterface<GetMCPWorkspacesParam, PaginationResponse<MCPSpace>>(
    'GET',
    '/mcp-plugin-center/rest/v1/workspaces'
);

type PutWorkspacesInfoParam = MCPSpace & WorkspaceIdParam;

/**
 * 修改空间信息
 */
export const apiPutWorkspacesInfo = createMCPInterface<PutWorkspacesInfoParam, void>(
    'PUT',
    '/mcp-plugin-center/rest/v1/workspaces/{workspaceId}'
);

/**
 * 新建空间
 */
export const apiPostMCPWorkspaces = createMCPInterface<MCPSpace, MCPSpace>(
    'POST',
    '/mcp-plugin-center/rest/v1/workspaces'
);

interface PutMCPWorkspaceRoleParam extends WorkspaceIdParam {
    memberList: MCPSpaceMember[];
}

/**
 * 修改空间成员权限
 */
export const apiPutMCPWorkspaceRole = createMCPInterface<PutMCPWorkspaceRoleParam, void>(
    'PUT',
    '/mcp-plugin-center/rest/v1/workspaces/role'
);

interface DeleteMCPWorkspaceRole extends WorkspaceIdParam {
    username: string;
}

/**
 * 删除空间成员权限
 */
export const apiDeleteMCPWorkspaceRole = createMCPInterface<DeleteMCPWorkspaceRole, void>(
    'DELETE',
    '/mcp-plugin-center/rest/v1/workspaces/{workspaceId}/users/{username}'
);

/**
 * 获取空间信息
 */
export const apiGetMCPWorkspaceInfo = createMCPInterface<WorkspaceIdParam, MCPSpace>(
    'GET',
    '/mcp-plugin-center/rest/v1/workspaces/{workspaceId}'
);

/**
 * 删除空间
 */
export const apiDeleteMCPWorkspace = createMCPInterface<WorkspaceIdParam, void>(
    'DELETE',
    '/mcp-plugin-center/rest/v1/workspaces/{workspaceId}'
);

export interface GetWorkspaceApplicationsParam extends WorkspaceIdParam {
    pn: number;
    size: number;
    keywords?: string;
    serverId?: number;
}

export interface GetWorkspacePermissionResult {
    admins: string[];
    hasSpaceAuth: boolean;
}

/**
 * 获取空间权限信息
 */
export const apiGetWorkspacePermission = createMCPInterface<WorkspaceIdParam, GetWorkspacePermissionResult>(
    'GET',
    '/mcp-plugin-center/rest/v1/workspaces/{workspaceId}/permission'
);

/**
 * 获取空间下应用列表
 */
export const apiGetWorkspaceApplications = createMCPInterface<
    GetWorkspaceApplicationsParam,
    PaginationResponse<MCPApplication>
>('GET', '/mcp-plugin-center/rest/v1/applications/{workspaceId}/list');

interface POSTMCPApplicationParam extends WorkspaceIdParam {
    name: string;
    description: string;
}

/**
 * 空间下新建应用
 */
export const apiPostMCPApplication = createMCPInterface<POSTMCPApplicationParam, MCPApplication>(
    'POST',
    '/mcp-plugin-center/rest/v1/applications/{workspaceId}'
);

interface MCPToolKey {
    toolKey: string;
}

interface MCPServerId {
    mcpServerId: number;
}

/**
 * 查询 mcp server 工具
 */
export const apiGetMCPTools = createMCPInterface<MCPServerId, MCPToolItem[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools'
    // {mock: MCP_SERVER_TOOL_LIST}
);

interface ToolId {
    toolId: number;
}

interface AppId {
    appId: number;
}

export const apiGetMCPToolItem = createMCPInterface<MCPServerId & ToolId, MCPToolItem>(
    'GET',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools/{toolId}'
);
/**
 * 保存 mcp server 工具
 */
export const apiPutMCPTool = createMCPInterface<MCPServerId & ToolId & MCPToolItem, MCPToolItem>(
    'PUT',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools/{toolId}'
);

/**
 * 新建 mcp server 工具
 */
export const apiPostMCPTool = createMCPInterface<MCPServerId & MCPToolItem, MCPToolItem>(
    'POST',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools'
);

/**
 * 删除 mcp server 工具
 */
export const apiDeleteServerTool = createMCPInterface<MCPServerId & ToolId>(
    'DELETE',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools/{toolId} '
);

/**
 * 保存 mcp server 工具
 */
export const apiGetMCPTool = createMCPInterface<MCPServerId & ToolId & AppId, MCPToolItem>(
    'GET',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools/{toolId}/{appId}'
);

interface UpsertToolConf {
    applicationId: number;
    serverId: number;
    toolId: number;
    responseTemplate: string;
}
/**
 * 更新响应模板
 */
export const apiPutMCPToolResponseTemplate = createMCPInterface<UpsertToolConf, void>(
    'PUT',
    '/mcp-plugin-center/rest/v1/applications/upsertToolConf'
);

/**
 * 注册 mcp server
 */
export const apiPostMCPServer = createMCPInterface<MCPServerBase, MCPServerBase>(
    'POST',
    '/mcp-plugin-center/rest/v1/mcp-servers'
);
/**
 * 注册 mcp server
 */
export const apiPutMCPServer = createMCPInterface<MCPServerId & MCPServerBase, MCPServerBase>(
    'PUT',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}'
);

/**

 * 查询 mcp server 详情
 */
export const apiGetMCPServer = createMCPInterface<MCPServerId, MCPServerBase>(
    'GET',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}'
);

/**
 * 查询广场 mcp server 列表
 */
export interface SquareServerListParams {
    pn?: number;
    size?: number;
    keywords?: string;
    platformType: 'hub'|'zone';
    serverSourceType?: string;
    serverProtocolType?: string;
    labels?: string;
    favorite?: boolean;
    isMine?: boolean;
    viewOrder?: 'DESC' | 'ASC';
    publishOrder?: 'DESC' | 'ASC';
    zoneId?: number;
}

export const apiGetSquareServerList = createMCPInterface<SquareServerListParams, PaginationResponse<MCPServerBase>>(
    'GET',
    '/mcp-plugin-center/rest/v1/hub/servers/list'
);
interface OpenApiUpload {
    content?: string;
    apiType?: string;
    swaggerUrl?: string;
}

export const apiPostOpenApiUpload = createMCPInterface<OpenApiUpload, ApiDefinition[]>(
    'POST',
    '/mcp-plugin-center/rest/v1/mcp-servers/apis/upload'
);

export type SpaceMCPServerParams = WorkspaceIdParam;

/**
 * 查询空间下 mcp server 列表
 */

export const apiGetMCPServerListByWorkspace = createMCPInterface<
    WorkspaceIdParam & {pn?: number, size?: number, status?: MCPServerStatus},
    PaginationResponse<MCPServerBase>
>('GET', '/mcp-plugin-center/rest/v1/mcp-servers/{workspaceId}/list');


export const apiPutApplicationSubscribe = createMCPInterface<{applicationId: number, serverId?: number}, null>(
    'PUT',
    '/mcp-plugin-center/rest/v1/applications/subscribeServer?applicationId={applicationId}&serverId={serverId}'
);

export const apiPutApplicationUnsubscribe = createMCPInterface<{applicationId: number, serverId?: number}, null>(
    'PUT',
    '/mcp-plugin-center/rest/v1/applications/unsubscribeServer?applicationId={applicationId}&serverId={serverId}'
);

export const apiGetSpaceLabels = createMCPInterface<WorkspaceIdParam, SpaceLabel[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/labels/{workspaceId}/list'
);

export const apiPostSpaceLabel = createMCPInterface<WorkspaceIdParam & {labelValue: string}, SpaceLabel>(
    'POST',
    '/mcp-plugin-center/rest/v1/labels/{workspaceId}'
);

interface LabelId {
    labelId: number;
}

export const apiDeleteSpaceLabel = createMCPInterface<LabelId>('DELETE', '/mcp-plugin-center/rest/v1/labels/{labelId}');

export const apiPostLogo = createMCPInterface<FormData, {fileLink: string}>(
    'POST',
    '/mcp-plugin-center/rest/v1/logo/upload'
);

interface PutServerSpaceParams extends MCPServerId {
    targetSpaceId: number;
}

export const apiPutServerSpace = createMCPInterface<PutServerSpaceParams>(
    'PUT',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/moveServer/{targetSpaceId}'
);

export const apiDeleteServer = createMCPInterface<MCPServerId>(
    'DELETE',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}'
);

export enum PublishType {
  HUB = 'hub',
  WORKSPACE = 'workspace'
}

export enum ChannelType {
  ZULU = 'COMATE',
  AI = 'AI_CENTER',
}

export enum VisibilityType {
  PUBLIC = 'PUBLIC',
  RANGE = 'RANGE',
}

// 仅原始接口使用，不向外暴露
interface PublishInfoResponse {
    // 发布位置
    publishType: string;
    // 发布范围(公开或指定范围)
    visibilityType?: VisibilityType;
    rangeContent?: {
        rangeType: string | null;
        content: string[] | null;
    };
    // 联系人
    contacts: string[];
    // 专区
    zoneId?: number;
    // 发布渠道
    channelType?: string;
}

/**
 *  发布到广场选公开时，visibilityType填PUBLIC,此时rangeContent可不填。
    发布到广场指定范围时，visibilityType填RANGE,rangeContent也要填。
    发布到本空间时visibilityType和rangeContent不用填。
    选择发布到专区时publishType 依然填hub,请求体加一个zoneId,不发布到专区则不用填zoneId。
    发布到AI能力中心时，只能是公开发布，不可选择范围
 */
export interface PublishInfo extends Omit<PublishInfoResponse, 'channelType' | 'publishType' | 'rangeContent'>{
    // 发布渠道
    channelType?: ChannelType[];
    publishType: PublishType[];
    rangeContent?: string[];
}

interface PublishParams extends MCPServerId {
    publishType: PublishType[];
    visibilityType?: VisibilityType;
    rangeContent?: string[];
    contacts: string[];
    channelType?: ChannelType[];
}

// 仅原始接口使用，不向外暴露
interface PublishRequestParams extends Omit<PublishParams, 'channelType' | 'publishType' | 'rangeContent'> {
    channelType?: string;
    publishType: PublishType;
    rangeContent?: {
        rangeType: string;
        content: string[];
    };
}

/**
 * 把比较恶心的数据结构转换放到接口层去做，不要引到业务层去处理
 */
export const apiGetPublishInfo = (params: MCPServerId) => {
    return createMCPInterface<MCPServerId, PublishInfoResponse | null>(
        'GET',
        '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/publishStatus'
    )(params).then(res => {
        if (res) {
            return {
                ...res,
                channelType: res.channelType ? res.channelType.split(',') : undefined,
                publishType: res.publishType === PublishType.WORKSPACE
                    ? [PublishType.WORKSPACE] : [PublishType.WORKSPACE, res.publishType],
                rangeContent: res.rangeContent ? res.rangeContent.content : undefined,
            };
        }
        return res;
    }) as Promise<PublishInfo|null>;
};

export const apiPutServerPublish = (params: PublishParams) => {
    const newParams = {
        ...params,
        channelType: params.channelType ? params.channelType.join(',') : undefined,
        // 默认发布到本空间
        publishType: params.publishType?.includes(PublishType.HUB) ? PublishType.HUB : PublishType.WORKSPACE,
        rangeContent: params.visibilityType === VisibilityType.RANGE ? {
            content: params.rangeContent,
            rangeType: 'EMAIL_GROUP',
        } : undefined,
    };
    return createMCPInterface<PublishRequestParams>(
        'PUT',
        '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/publish'
    )(newParams);
};

export const apiPostViewCount = createMCPInterface<MCPServerId>(
    'POST',
    '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/view'
);

export const apiPutServerFavorite = createMCPInterface<MCPServerId>(
    'PUT',
    '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/favorite'
);

export const apiDeleteServerFavorite = createMCPInterface<MCPServerId>(
    'DELETE',
    '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/favorite'
);

export const apiGetAppListForServer = createMCPInterface<MCPServerId, AppListForMCPServerBase[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/applications'
);

interface SubscribeParams{
    applicationId: number;
    serverId: number;
    subscribeToolIds: number[];
    params: MCPServerParam[];
}

export const apiPostServerSubscribe = createMCPInterface<SubscribeParams, null>(
    'POST',
    '/mcp-plugin-center/rest/v1/hub/servers/subscribe'
);

export const apiGetDefaultLabels = createMCPInterface<void, SpaceLabel[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/labels/globalList'
);

export interface SpaceInitParams{
    value: string;
    label: string;
}

export const apiPostSpaceInit = createMCPInterface<SpaceInitParams, MCPSpace>(
    'POST',
    '/mcp-plugin-center/rest/v1/workspaces/init '
);

interface CancelPublishParams {
    publishType: string;
    mcpServerId: number;
}

export const apiPostCancelPublish = createMCPInterface<CancelPublishParams>(
    'PUT',
    '/mcp-plugin-center/rest/v1/hub/servers/{mcpServerId}/cancelPub?publishType={publishType}'
);

export const apiGetEmailGroup = createMCPInterface<{prefix: string}, string[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/users/emails'
);

interface GlobalVar {
    'name': string;
    'dataType': string;
    'description': string | null;
    'value': string | null;
    'required': boolean;
}

export const apiGetMCPGlobalVars = createMCPInterface<MCPServerId, GlobalVar[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/playground/params/{mcpServerId}'
);

export const apiPostMCPGlobalVars = createMCPInterface<
    {params: GlobalVar[], serverId: number}>(
        'POST',
        '/mcp-plugin-center/rest/v1/playground/params'
    );

interface MCPToolDebugParams {
    serverParams: MCPServerParam[];
    toolParams: BaseParam[];
}

interface MCPToolKey {
    toolKey: string;
}
// 调试工具
export const apiPostMCPToolDebug = createMCPInterface<MCPServerId & MCPToolKey & MCPToolDebugParams, string | object>(
    'POST',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/tools/{toolKey}'
);

interface McpPlaygroundQueryParams {
    query: string;
    conversationId?: string;
}

export const apiPostMcpPlaygroundQuery = createMCPInterface<McpPlaygroundQueryParams, {conversationId: string}>(
    'POST',
    '/mcp-plugin-center/rest/v1/playground/query'
);

export const apiPostMcpPlaygroundCancel = createMCPInterface<{conversationId: string}, {conversationId: string}>(
    'POST',
    '/mcp-plugin-center/rest/v1/playground/query/cancel'
);

interface OriginalAnnouncement {
    'id': number;
    'announcement': string;
    // 是否被用户关闭
    'ifCancel': boolean;
}

export type Announcement = Omit<OriginalAnnouncement, 'ifCancel'>;

export interface MCPZoneDetail<T> {
    'id': string;
    'name': string;
    'type': string;
    'description': string;
    'announcement': T;
    'publisher': string[];
    'department': string;
    'icon': string;
    'serverCount': number | null;
    'subCount': number | null;
    'commentCount': number | null;
    'childZones': Array<MCPZoneDetail<T>>;
}

function formateMCPZone(zone: MCPZoneDetail<OriginalAnnouncement>): MCPZoneDetail<Announcement> {
    return {
        id: zone.id.toString(),
        name: zone.name,
        type: zone.type,
        description: zone.description ?? '',
        // 被用户关了就不必返给UI层了
        announcement: zone.announcement && !zone.announcement.ifCancel ? {
            id: zone.announcement.id,
            announcement: zone.announcement.announcement,
        } : null,
        publisher: zone.publisher ?? [],
        department: zone.department ?? '',
        icon: zone.icon,
        serverCount: zone.serverCount ?? 0,
        subCount: zone.subCount ?? 0,
        commentCount: zone.commentCount ?? 0,
        childZones: zone.childZones ? zone.childZones.map(formateMCPZone) : [],
    };
}

export const apiGetMCPZoneDetail = (zoneId: string) =>
    createMCPInterface<{zoneId: string}, MCPZoneDetail<OriginalAnnouncement>>(
        'GET',
        '/mcp-plugin-center/rest/v1/zone/{zoneId}'
    )({zoneId}).then(res => formateMCPZone(res));


export const apiGetAllZones = createMCPInterface<void, Array<MCPZoneDetail<OriginalAnnouncement>>>(
    'GET',
    '/mcp-plugin-center/rest/v1/zone/list '
);

// 获取专区默认场景
export const apiGetZoneScenes = createMCPInterface<{zoneId: string}, SpaceLabel[]>(
    'GET',
    '/mcp-plugin-center/rest/v1/labels/{zoneId}/zoneList'
);

export const apiGetHubAnnouncement:
() => Promise<Omit<Announcement, 'ifCancel'>|null> = () => createMCPInterface<void, OriginalAnnouncement>(
    'GET',
    '/mcp-plugin-center/rest/v1/hub/servers/announcement'
)().then(res => {
    if (!res.ifCancel) {
        return {
            id: res.id,
            announcement: res.announcement,
        };
    }
    return null;
});

export const apiGetMCPServerData = createMCPInterface<MCPServerId, MCPServerAnalysisData>(
    'GET',
    '/mcp-plugin-center/rest/v1/mcp-servers/{mcpServerId}/data'
);

export const apiGetToolLogList = createMCPInterface<MCPToolLogListParams, PaginationResponse<MCPToolLogItem>>(
    'GET',
    '/mcp-plugin-center/rest/v1/toolLog/list'
);
