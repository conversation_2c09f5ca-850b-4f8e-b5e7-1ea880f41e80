/* eslint-disable complexity */
import {Flex, Space, Typography, Divider, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {useCallback, useRef, useEffect, useState} from 'react';
import {formatCount} from '@/utils/mcp/format';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {useMCPServerId, useMCPWorkspaceId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {IconCallCount, IconEye} from '@/icons/mcp';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import DescriptionItem from '@/design/MCP/MCPDescriptionItem';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import GoBackButton from '@/components/MCP/GoBackButton';
import PublishInfo from '@/components/MCP/PublishInfo';
import {MCPSpaceLink, MCPSquareLink} from '@/links/mcp';

const DescriptionWrapper = styled.div`
    background-color: #F8F8F8;
    padding: 10px 12px;
    color: #3C3C3C;
    position: relative;
`;

const DescriptionContainer = styled.div`
    font-size: 14px;
    line-height: 22px;
    position: relative;
    max-height: 66px;
    overflow: hidden;
`;

const DescriptionText = styled.div`
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
`;

const MCPInfo = () => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const workspaceId = useMCPWorkspaceId();
    const descriptionRef = useRef<HTMLDivElement>(null);
    const [isDescriptionOverflowing, setIsDescriptionOverflowing] = useState(false);

    const refresh = useCallback(
        () => {
            loadMCPServer({mcpServerId});
        },
        [mcpServerId]
    );

    const backUrl = workspaceId
        ? MCPSpaceLink.toUrl({workspaceId})
        : MCPSquareLink.toUrl();

    useEffect(
        () => {
            const checkOverflow = () => {
                if (descriptionRef.current) {
                    const element = descriptionRef.current;
                    const isOverflowing = element.scrollHeight > element.clientHeight;
                    setIsDescriptionOverflowing(isOverflowing);
                }
            };

            checkOverflow();
            window.addEventListener('resize', checkOverflow);
            return () => window.removeEventListener('resize', checkOverflow);
        },
        [mcpServer?.description]
    );

    return (
        <Flex vertical gap={16}>
            <Flex gap={14} align="center" justify="space-between">
                <Flex align="center" gap={16}>
                    <GoBackButton size={24} url={backUrl} />
                    <MCPServerAvatar size={40} icon={mcpServer?.icon} radius={4} />
                    <Flex align="flex-start" vertical>
                        <Typography.Title level={4} style={{fontSize: 16, color: '#181818'}}>
                            {mcpServer?.name}
                        </Typography.Title>
                        <Flex align="center" style={{fontSize: 12, color: '#8F8F8F'}}>
                            {getServerTypeText(mcpServer?.serverSourceType)}
                            <Divider type="vertical" style={{borderColor: '#D9D9D9', margin: '0 12px'}} />
                            {mcpServer?.serverProtocolType}
                            <Divider type="vertical" style={{borderColor: '#D9D9D9', margin: '0 12px'}} />
                            <PublishInfo
                                username={mcpServer?.lastModifyUser}
                                time={mcpServer?.lastModifyTime}
                            />
                        </Flex>
                    </Flex>
                </Flex>
                <Flex>
                    <Space split={<Divider type="vertical" style={{borderColor: '#D9D9D9'}} />}>
                        <Flex align="center" gap={12} style={{color: '#545454'}}>
                            <Tooltip title="浏览量">
                                <Flex align="center" gap={4}>
                                    <IconEye />
                                    {formatCount(mcpServer?.serverMetrics?.viewCount ?? 0)}
                                </Flex>
                            </Tooltip>
                            <Tooltip title="调用量">
                                <Flex align="center" gap={4}>
                                    <IconCallCount />
                                    {formatCount(mcpServer?.serverMetrics?.callCount ?? 0)}
                                </Flex>
                            </Tooltip>
                        </Flex>
                        <MCPCollectButton
                            refresh={refresh}
                            favorite={mcpServer?.favorite}
                            serverId={mcpServerId}
                        />
                        <MCPSubscribeButton
                            id={mcpServerId}
                            workspaceId={mcpServer?.workspaceId}
                            showText
                        />
                    </Space>
                </Flex>
            </Flex>
            <DescriptionWrapper>
                {mcpServer?.description && isDescriptionOverflowing ? (
                    <Tooltip title={mcpServer.description} placement="top">
                        <DescriptionContainer ref={descriptionRef}>
                            <DescriptionText>{mcpServer.description}</DescriptionText>
                        </DescriptionContainer>
                    </Tooltip>
                ) : (
                    <DescriptionContainer ref={descriptionRef}>
                        <DescriptionText>{mcpServer?.description || '暂无描述'}</DescriptionText>
                    </DescriptionContainer>
                )}
            </DescriptionWrapper>
            <Flex align="center" gap={40} style={{marginTop: '4px'}}>
                {mcpServer?.labels && mcpServer.labels.length > 0 && (
                    <DescriptionItem label="场景" labelStyle={{color: '#8F8F8F'}}>
                        <TagGroup
                            labels={mcpServer.labels.map(
                                label => ({id: label.id, label: label.labelValue})
                            )}
                            color="light-purple"
                            maxNum={3}
                        />
                    </DescriptionItem>
                )}
                <DescriptionItem label="部门" labelStyle={{color: '#8F8F8F'}}>
                    {mcpServer?.departmentName || '暂无部门信息'}
                </DescriptionItem>
                <DescriptionItem label="联系人" labelStyle={{color: '#8F8F8F'}}>
                    <UserAvatarList users={mcpServer?.contacts ?? []} max={2} />
                </DescriptionItem>
            </Flex>
        </Flex>
    );
};

export default MCPInfo;
