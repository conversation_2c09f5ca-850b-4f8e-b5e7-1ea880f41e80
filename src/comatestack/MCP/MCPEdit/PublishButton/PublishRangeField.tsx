import {Form, Flex, Radio} from 'antd';
import {EmailGroupSelect} from '@/components/MCP/EmailGroupSelect';
import {ChannelType, VisibilityType} from '@/api/mcp';

export const PublishRangeField = () => {
    return (
        <Form.Item label="发布范围" labelCol={{span: 4}} required>
            <Flex vertical gap={8}>
                <Form.Item noStyle dependencies={['channelType']}>
                    {
                        ({getFieldValue}) => {
                            const shouldPublic = getFieldValue('channelType')?.includes(ChannelType.AI);
                            return (
                                <Form.Item name="visibilityType" style={{marginBottom: 0}}>
                                    <Radio.Group>
                                        <Radio value={VisibilityType.PUBLIC}>公开</Radio>
                                        <Radio value={VisibilityType.RANGE} disabled={shouldPublic}>
                                            指定范围
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            );
                        }
                    }
                </Form.Item>
                {/* 使用dependencies有时会漏掉一些更新，故使用shouldUpdate */}
                <Form.Item noStyle shouldUpdate={(pre, cur) => pre.visibilityType !== cur.visibilityType}>
                    {({getFieldValue}) => {
                        const visibilityTypeValue = getFieldValue('visibilityType');
                        return visibilityTypeValue === VisibilityType.RANGE ? (
                            <Form.Item
                                name="rangeContent"
                                rules={[{
                                    required: true,
                                    message: '请选择邮箱或邮件组',
                                }]}
                            >
                                <EmailGroupSelect widthUser />
                            </Form.Item>
                        ) : null;
                    }}
                </Form.Item>
            </Flex>
        </Form.Item>
    );
};
