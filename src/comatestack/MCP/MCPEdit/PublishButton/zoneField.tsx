import {Select, Form} from 'antd';
import {useRequest} from 'huse';
import {apiGetAllZones} from '@/api/mcp';

const getAllZones = () => apiGetAllZones().then(res => {
    const flatten: Array<{label: string, value: string}> = [];
    // 只有两层，无需递归
    res.forEach(zone => {
        flatten.push({label: zone.name, value: zone.id});
        zone.childZones.forEach(child => {
            flatten.push({label: `${zone.name}/${child.name}`, value: child.id});
        });
    });
    return flatten;
});

export const ZoneField = () => {
    const {data: zones} = useRequest(getAllZones, {});
    return (
        <Form.Item
            labelCol={{span: 4}}
            label="专区"
            name="zoneId"
        >
            <Select options={zones} allowClear placeholder="请根据MCP的适用场景选择专区" />
        </Form.Item>
    );
};
