import {message, Modal} from '@panda-design/components';
import {Alert, Form} from 'antd';
import {useCallback, useEffect} from 'react';
import {useRequest} from 'huse';
import {useMCPServerId} from '@/components/MCP/hooks';
import {apiGetPublishInfo, apiPutServerPublish, ChannelType, PublishType, VisibilityType} from '@/api/mcp';
import {loadMCPServer} from '@/regions/mcp/mcpServer';
import {UserMultipleSelect} from '@/components/ievalue/UserMultipleSelect';
import {Gap} from '@/design/iplayground/Gap';
import {PublishTypeField} from './PublishTypeField';
import {PublishRangeField} from './PublishRangeField';
import {
    FormValues,
    initFormValue,
} from './types';
import {ChannelTypeField} from './ChannelTypeField';
import {ZoneField} from './zoneField';

interface Props {
  onCancel: () => void;
}

export const ConfirmModal = ({onCancel}: Props) => {
    const [form] = Form.useForm<FormValues>();
    const publishTypeValue = Form.useWatch('publishType', form);
    const publishToHub = publishTypeValue?.includes(PublishType.HUB);
    const mcpServerId = useMCPServerId();
    const {data: publishInfo, pending} = useRequest(apiGetPublishInfo, {mcpServerId});
    useEffect(
        () => {
            if (!pending && publishInfo) {
                form.setFieldsValue(publishInfo);
            }
        },
        [form, pending, publishInfo]
    );
    const handleOk = useCallback(
        async () => {
            try {
                const values = await form.validateFields();
                await apiPutServerPublish({
                    mcpServerId,
                    ...values,
                });
                loadMCPServer({mcpServerId});
                message.success('发布成功');
                onCancel();
            } catch (e) {
                if (e.errorFields) {
                    // 说明是表单验证失败
                } else {
                    message.error('发布失败:', e.response?.data?.msg ?? '');
                }
            }
        },
        [mcpServerId, onCancel, form]
    );

    const onValuesChange = useCallback(
        (changedValues: any) => {
            if (changedValues.channelType?.includes(ChannelType.AI)) {
                form.setFieldValue('visibilityType', VisibilityType.PUBLIC);
            }
        },
        [form]
    );

    return (
        <Modal
            title="发布"
            open
            onCancel={onCancel}
            onOk={handleOk}
        >
            <Alert message="MCP Server需发布后才可以调用，发布到广场可以共享给其他开发者使用。" />
            <Gap />
            {/* 这里必须强制设置disabled为true，
                因为该组件嵌套在MCPEdit组件下,
                而MCPEdit在中有一个form实例,
                在某些情况下MCPEdit中的实例会被设为disabled,
                从而影响此处的form实例。
            */}
            <Form
                form={form}
                name="release"
                initialValues={initFormValue}
                disabled={false}
                onValuesChange={onValuesChange}
            >
                {/* 发布位置 */}
                <PublishTypeField />
                {
                    publishToHub && (
                        <>
                            {/* 专区 */}
                            <ZoneField />
                            {/* 发布渠道 */}
                            <ChannelTypeField />
                            {/* 发布范围 */}
                            <PublishRangeField />
                        </>
                    )
                }
                <Form.Item
                    labelCol={{span: 4}}
                    name="contacts"
                    label="联系人"
                    rules={[{required: true, message: '请选择联系人'}]}
                >
                    <UserMultipleSelect mode="multiple" placeholder="请输入该MCP Server的联系人，方便使用者咨询" labelInValue={false} />
                </Form.Item>
            </Form>
        </Modal>
    );
};
