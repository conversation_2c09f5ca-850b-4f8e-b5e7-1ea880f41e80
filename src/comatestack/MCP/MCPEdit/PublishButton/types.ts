import {ChannelType, PublishInfo, PublishType, VisibilityType} from '@/api/mcp';

export interface FormValues extends Omit<PublishInfo, 'rangeContent'|'publishType'|'channelType'> {
  rangeContent?: string[];
  channelType?: ChannelType[];
  publishType: PublishType[];
}

export const initFormValue: FormValues = {
    publishType: [PublishType.WORKSPACE],
    visibilityType: VisibilityType.PUBLIC,
    contacts: [],
};

export const PUBLISH_TYPE_OPTIONS = [
    {label: '空间内', value: PublishType.WORKSPACE, disabled: true},
    {label: '广场', value: PublishType.HUB},
];

export const CHANNEL2_OPTIONS = [
    {label: '发布到Zulu MCP广场', value: ChannelType.ZULU},
    {label: '发布到AI能力中心', value: ChannelType.AI},
];
