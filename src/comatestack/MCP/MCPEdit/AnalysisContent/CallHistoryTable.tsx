import {Table, Tag} from 'antd';
import {ColumnsType} from 'antd/es/table';
import {Button} from '@panda-design/components';
import {MCPToolLogItem} from '@/types/mcp/mcp';
import {formatISOTime} from '@/utils/date';
import {IconDetail} from '@/icons/mcp';

interface Props {
    dataSource: MCPToolLogItem[];
    loading: boolean;
    pagination: {
        current: number;
        pageSize: number;
        total: number;
    };
    sortOrder: {
        createTimeOrder?: 'DESC' | 'ASC';
        durationTimeOrder?: 'DESC' | 'ASC';
        idOrder?: 'DESC' | 'ASC';
    };
    onTableChange: (pagination: any, filters: any, sorter: any) => void;
    onPaginationChange: (page: number, pageSize: number) => void;
    onViewDetail: (record: MCPToolLogItem) => void;
}

const CallHistoryTable = ({
    dataSource,
    loading,
    pagination,
    sortOrder,
    onTableChange,
    onPaginationChange,
    onViewDetail,
}: Props) => {
    const columns: ColumnsType<MCPToolLogItem> = [
        {
            title: '调用ID',
            dataIndex: 'id',
            key: 'id',
            sorter: true,
            sortOrder: sortOrder.idOrder === 'DESC'
                ? 'descend'
                : sortOrder.idOrder === 'ASC'
                    ? 'ascend'
                    : null,
        },
        {
            title: '时间',
            dataIndex: 'createTime',
            key: 'createTime',
            sorter: true,
            sortOrder: sortOrder.createTimeOrder === 'DESC'
                ? 'descend'
                : sortOrder.createTimeOrder === 'ASC'
                    ? 'ascend'
                    : null,
            render: (time: string) => formatISOTime(time),
        },
        {
            title: '耗时',
            dataIndex: 'durationTime',
            key: 'durationTime',
            sorter: true,
            sortOrder: sortOrder.durationTimeOrder === 'DESC'
                ? 'descend'
                : sortOrder.durationTimeOrder === 'ASC'
                    ? 'ascend'
                    : null,
            render: (durationTime: number) => `${durationTime ? durationTime : '- '}ms`,
        },
        {
            title: '工具',
            dataIndex: 'toolName',
            key: 'toolName',
        },
        {
            title: '状态',
            dataIndex: 'ifSuccess',
            key: 'ifSuccess',
            render: (ifSuccess: boolean) => (
                <Tag color={ifSuccess ? 'success' : 'error'}>
                    {ifSuccess ? '成功' : '失败'}
                </Tag>
            ),
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Button
                    type="text"
                    size="small"
                    onClick={() => onViewDetail(record)}
                    icon={<IconDetail style={{fontSize: 14}} />}
                    style={{fontSize: 14}}
                >
                    日志
                </Button>
            ),
        },
    ];

    return (
        <Table
            columns={columns}
            dataSource={dataSource}
            rowKey="id"
            loading={loading}
            pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                onChange: onPaginationChange,
                showSizeChanger: true,
                showQuickJumper: true,
            }}
            onChange={onTableChange}
        />
    );
};

export default CallHistoryTable;
