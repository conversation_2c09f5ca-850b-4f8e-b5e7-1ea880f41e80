import styled from '@emotion/styled';


export const ModalHeader = styled.div`
    margin-left: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
`;

export const ModalTitle = styled.h4`
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #000;
`;

export const ToolName = styled.span`
    font-size: 12px;
    line-height: 20px;
    color: #666;
`;

export const Divider = styled.div`
    width: 1px;
    height: 16px;
    background-color: #d9d9d9;
`;

export const SectionTitle = styled.h4`
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 10px 0;
    color: #000;
`;

export const ContentBlock = styled.div`
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    background-color: #fff;
`;

export const ContentText = styled.pre`
    font-size: 14px;
    line-height: 22px;
    color: #181818;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
`;

export const CollapsibleSection = styled.div`
    margin-top: 8px;
    padding-left: 24px;
    border-left: 1px dashed color-mix(in srgb, var(--Color-neutral-Gray8, #545454) 20%, transparent);
`;


export const CollapsibleHeader = styled.div`
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    color: #666;

    &:hover {
        color: #1890ff;
    }
`;

export const CollapsibleContent = styled.div`
    margin-top: 10px;
    padding: 12px 16px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    background-color: #fff;
`;
