import {useState, useCallback, useEffect} from 'react';
import {apiGetMCPServerData, apiGetToolLogList} from '@/api/mcp';
import {MCPServerAnalysisData, MCPToolLogItem, MCPServerBase} from '@/types/mcp/mcp';

export const useServerAnalysisData = (mcpServerId: number) => {
    const [data, setData] = useState<MCPServerAnalysisData | null>(null);
    const [loading, setLoading] = useState(false);

    const loadData = useCallback(
        async () => {
            if (!mcpServerId) {
                return;
            }
            setLoading(true);
            try {
                const result = await apiGetMCPServerData({mcpServerId});
                setData(result);
            } catch (error) {
                console.error('Failed to load server data:', error);
            } finally {
                setLoading(false);
            }
        },
        [mcpServerId]
    );

    useEffect(
        () => {loadData();},
        [loadData]
    );

    return {data, loading, reload: loadData};
};

export const useToolLogList = (mcpServer: MCPServerBase | null | undefined) => {
    const [dataSource, setDataSource] = useState<MCPToolLogItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [sortOrder, setSortOrder] = useState<{
        createTimeOrder?: 'DESC' | 'ASC';
        durationTimeOrder?: 'DESC' | 'ASC';
        idOrder?: 'DESC' | 'ASC';
    }>({});

    const loadData = useCallback(
        async (params?: {
        pn?: number;
        size?: number;
        createTimeOrder?: 'DESC' | 'ASC';
        durationTimeOrder?: 'DESC' | 'ASC';
        idOrder?: 'DESC' | 'ASC';
    }) => {
            if (!mcpServer?.serverKey) {
                return;
            }

            setLoading(true);
            try {
                const response = await apiGetToolLogList({
                    serverKey: mcpServer.serverKey,
                    pn: params?.pn || pagination.current,
                    size: params?.size || pagination.pageSize,
                    ...params,
                });
                setDataSource(response.records || []);
                setPagination(prev => ({
                    ...prev,
                    current: params?.pn || prev.current,
                    total: response.total || 0,
                }));
            } catch (error) {
                console.error('Failed to load tool log list:', error);
            } finally {
                setLoading(false);
            }
        },
        [mcpServer?.serverKey, pagination]
    );

    const handleTableChange = useCallback(
        (paginationInfo: any, _filters: any, sorter: any) => {
            const newSortOrder: typeof sortOrder = {};
            if (sorter.field === 'id') {
                newSortOrder.idOrder = sorter.order === 'descend'
                    ? 'DESC'
                    : sorter.order === 'ascend'
                        ? 'ASC'
                        : undefined;
            } else if (sorter.field === 'createTime') {
                newSortOrder.createTimeOrder = sorter.order === 'descend'
                    ? 'DESC'
                    : sorter.order === 'ascend'
                        ? 'ASC'
                        : undefined;
            } else if (sorter.field === 'durationTime') {
                newSortOrder.durationTimeOrder = sorter.order === 'descend'
                    ? 'DESC'
                    : sorter.order === 'ascend'
                        ? 'ASC'
                        : undefined;
            }

            setSortOrder(newSortOrder);
            loadData({
                pn: paginationInfo.current,
                size: paginationInfo.pageSize,
                ...newSortOrder,
            });
        },
        [loadData]
    );

    const handlePaginationChange = useCallback(
        (page: number, pageSize: number) => {
            setPagination(prev => ({
                ...prev,
                current: page,
                pageSize,
            }));
            loadData({
                pn: page,
                size: pageSize,
                ...sortOrder,
            });
        },
        [loadData, sortOrder]
    );

    useEffect(
        () => {
            if (mcpServer?.serverKey) {
                loadData();
            }
        },
        [loadData, mcpServer?.serverKey]
    );

    return {
        dataSource,
        loading,
        pagination,
        sortOrder,
        handleTableChange,
        handlePaginationChange,
        reload: loadData,
    };
};
