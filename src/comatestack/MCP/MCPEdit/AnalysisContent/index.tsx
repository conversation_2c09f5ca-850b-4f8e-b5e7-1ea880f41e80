import {useState} from 'react';
import {Flex} from 'antd';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import {MCPToolLogItem} from '@/types/mcp/mcp';
import {useServerAnalysisData} from './useServerAnalysisData';
import {useToolLogList} from './useToolLogList';
import {SectionWrapper, SectionTitle} from './styles';
import MetricsCards from './MetricsCards';
import ToolDetailTable from './ToolDetailTable';
import CallHistoryTable from './CallHistoryTable';
import ToolCallHistoryModal from './ToolCallHistoryModal';
import CallHistoryFilter from './CallHistoryFilter';
import {useAllToolNames} from './useAllToolNames';

const AnalysisContent = () => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);

    const [modalVisible, setModalVisible] = useState(false);
    const [selectedToolLogItem, setSelectedToolLogItem] = useState<MCPToolLogItem | null>(null);
    const [filters, setFilters] = useState<{
        toolName?: string;
        ifSuccess?: boolean;
    }>({});

    const {data: serverData} = useServerAnalysisData(mcpServerId);

    const {
        dataSource: toolLogList,
        loading: toolLogLoading,
        pagination: toolLogPagination,
        sortOrder,
        handleTableChange,
        handlePaginationChange,
    } = useToolLogList(mcpServer, filters);

    const {toolNames} = useAllToolNames(mcpServer);

    const handleViewDetail = (record: MCPToolLogItem) => {
        setSelectedToolLogItem(record);
        setModalVisible(true);
    };

    const handleCloseModal = () => {
        setModalVisible(false);
        setSelectedToolLogItem(null);
    };

    const handleFilterChange = (newFilters: {toolName?: string, ifSuccess?: boolean}) => {
        setFilters(newFilters);
    };

    const handleResetFilters = () => {
        setFilters({});
    };

    return (
        <div>
            <SectionWrapper>
                <SectionTitle>MCP Server核心指标</SectionTitle>
                <MetricsCards data={serverData} />
            </SectionWrapper>

            <SectionWrapper>
                <SectionTitle>工具详情</SectionTitle>
                <ToolDetailTable data={serverData} />
            </SectionWrapper>

            <SectionWrapper>
                <Flex justify="space-between" align="center" style={{marginBottom: 16}}>
                    <SectionTitle style={{margin: 0}}>调用历史</SectionTitle>
                    <CallHistoryFilter
                        filters={filters}
                        toolNames={toolNames}
                        onFilterChange={handleFilterChange}
                        onResetFilters={handleResetFilters}
                    />
                </Flex>
                <CallHistoryTable
                    dataSource={toolLogList}
                    loading={toolLogLoading}
                    pagination={toolLogPagination}
                    sortOrder={sortOrder}
                    onTableChange={handleTableChange}
                    onPaginationChange={handlePaginationChange}
                    onViewDetail={handleViewDetail}
                />
            </SectionWrapper>

            <ToolCallHistoryModal
                visible={modalVisible}
                onClose={handleCloseModal}
                toolLogItem={selectedToolLogItem}
            />
        </div>
    );
};

export default AnalysisContent;
