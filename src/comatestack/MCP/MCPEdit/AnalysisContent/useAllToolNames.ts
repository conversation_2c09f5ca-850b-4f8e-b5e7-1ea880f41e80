import {useState, useCallback, useEffect} from 'react';
import {apiGetToolLogList} from '@/api/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';

export const useAllToolNames = (mcpServer: MCPServerBase | null) => {
    const [toolNames, setToolNames] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);

    const loadAllToolNames = useCallback(
        async () => {
            if (!mcpServer?.serverKey) {
                return;
            }

            setLoading(true);
            try {
                const response = await apiGetToolLogList({
                    serverKey: mcpServer.serverKey,
                    pn: 1,
                    size: 1000,
                });
                const toolNameList = response.records?.map(item => item.toolName)
                    .filter((name): name is string => Boolean(name)) || [];
                const uniqueToolNames = Array.from(new Set(toolNameList));
                setToolNames(uniqueToolNames);
            } catch (error) {
                console.error('Failed to load all tool names:', error);
            } finally {
                setLoading(false);
            }
        },
        [mcpServer?.serverKey]
    );

    useEffect(
        () => {
            if (mcpServer?.serverKey) {
                loadAllToolNames();
            }
        },
        [loadAllToolNames, mcpServer?.serverKey]
    );

    return {toolNames, loading, reload: loadAllToolNames};
};
