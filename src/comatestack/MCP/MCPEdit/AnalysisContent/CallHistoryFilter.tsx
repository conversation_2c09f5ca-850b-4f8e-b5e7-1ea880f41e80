import {Flex, Select} from 'antd';
import {Button} from '@panda-design/components';
import styled from '@emotion/styled';

const StyledSelect = styled(Select)`
    width: 240px;
`;

interface Props {
    filters: {
        toolName?: string;
        ifSuccess?: boolean;
    };
    toolNames: string[];
    onFilterChange: (filters: {toolName?: string, ifSuccess?: boolean}) => void;
    onResetFilters: () => void;
}

const CallHistoryFilter = ({filters, toolNames, onFilterChange, onResetFilters}: Props) => {
    const toolNameOptions = [
        {label: '全部工具', value: 'ALL_TOOLS'},
        ...toolNames.map(name => ({label: name, value: name})),
    ];

    const statusOptions = [
        {label: '全部状态', value: 'ALL_STATUS'},
        {label: '成功', value: true},
        {label: '失败', value: false},
    ];

    const handleToolNameChange = (value: unknown) => {
        const toolName = value === 'ALL_TOOLS' ? undefined : (value as string);
        onFilterChange({
            ...filters,
            toolName,
        });
    };

    const handleStatusChange = (value: unknown) => {
        const ifSuccess = value === 'ALL_STATUS' ? undefined : (value as boolean);
        onFilterChange({
            ...filters,
            ifSuccess,
        });
    };

    return (
        <Flex align="center" justify="flex-end" gap={12}>
            <StyledSelect
                placeholder="选择工具"
                value={filters.toolName || 'ALL_TOOLS'}
                options={toolNameOptions}
                onChange={handleToolNameChange}
                allowClear={false}
            />
            <StyledSelect
                placeholder="选择状态"
                value={filters.ifSuccess !== undefined ? filters.ifSuccess : 'ALL_STATUS'}
                options={statusOptions}
                onChange={handleStatusChange}
                allowClear={false}
            />
            <Button type="text" onClick={onResetFilters}>
                重置筛选
            </Button>
        </Flex>
    );
};

export default CallHistoryFilter;
