import {Divider, Flex, Typography} from 'antd';
import {CSSProperties} from 'react';
import styled from '@emotion/styled';
import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
import {useMCPServerId, useMCPWorkspaceId} from '@/components/MCP/hooks';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {MCPSpaceLink} from '@/links/mcp';
import GoBackButton from '@/components/MCP/GoBackButton';
import UpdateInfo from '@/components/MCP/UpdateInfo';
import {overflowHiddenCss} from '@/styles/components';

const Wrapper = styled(Flex)`
    position: absolute;
    left: 24px;
    height: 100%;
    overflow: hidden;
    z-index: 1;
`;

interface Props {
    style?: CSSProperties;
}
export default function BaseInfo({style}: Props) {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const spaceId = useMCPWorkspaceId();

    if (!mcpServer) {
        return null;
    }
    return (
        <Wrapper align="center" gap={8} style={style}>
            <GoBackButton size={24} url={MCPSpaceLink.toUrl({workspaceId: spaceId})} />
            <Flex gap={16} align="center" className={overflowHiddenCss}>
                <MCPServerAvatar size={40} icon={mcpServer?.icon} radius={4} />
                <Flex vertical className={overflowHiddenCss}>
                    <Flex align="center">
                        <Typography.Title level={4} ellipsis>
                            {mcpServer?.name}
                        </Typography.Title>
                        <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                        <MCPReleaseStatus
                            status={mcpServer.serverStatus || 'draft'}
                            publishType={mcpServer.serverPublishType}
                        />
                    </Flex>
                    <UpdateInfo username={mcpServer?.lastModifyUser} time={mcpServer?.lastModifyTime} />
                </Flex>
            </Flex>
        </Wrapper>
    );
}
