import {Flex, Segmented, TabsProps} from 'antd';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import EllipsisButton from '@/components/MCP/EllipsisButton';
import {MCPEditTab} from '@/types/mcp/mcp';
import PublishButton from '../PublishButton';
import MoveButton from '../MoveButton';
import DeleteButton from '../DeleteButton';
import RepealButton from '../RepealButton';
import ActionButtons from '../ActionButtons';
import BaseInfo from './BaseInfo';

const Wrapper = styled.div`
    position: relative;
    margin-top: 16px;
`;

const StyledSegmented = styled(Segmented)`
    .ant-segmented-item {
        padding: 5px 12px !important;
    }

    .ant-segmented-item-selected {
        position: relative;

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 1px solid var(--Tokens-primary-color, #0080FF);
            border-radius: inherit;
            pointer-events: none;
            z-index: 1;
        }
    }

    /* 备用方案：直接设置边框 */
    &&.ant-segmented .ant-segmented-item.ant-segmented-item-selected {
        border: 1px solid var(--Tokens-primary-color, #0080FF) !important;
        box-shadow: 0 0 0 1px var(--Tokens-primary-color, #0080FF) !important;
    }
`;

const items = [
    {
        key: MCPEditTab.ServerInfo,
        label: '基本信息',
    },
    {
        key: MCPEditTab.Tools,
        label: '工具配置',
    },
    {
        key: MCPEditTab.Analysis,
        label: '数据分析',
    },
];

type Props = TabsProps;
export default function Header({activeKey, onChange}: Props) {

    return (
        <Wrapper>
            <BaseInfo style={{maxWidth: 'calc(50% - 160px)'}} />
            <div style={{display: 'flex', justifyContent: 'center'}}>
                <StyledSegmented
                    options={items.map(item => ({
                        label: item.label,
                        value: item.key,
                    }))}
                    value={activeKey}
                    onChange={onChange as (value: unknown) => void}
                    size="large"
                />
            </div>
            <Flex
                align="center"
                gap={8}
                style={{position: 'absolute', right: 24, top: 0, height: '100%'}}
            >
                <RepealButton />
                <ActionButtons activeTab={activeKey as MCPEditTab} />
                <PublishButton />
                <EllipsisButton>
                    <Button
                        type="text"
                        disabled
                        tooltip="即将上线"
                    >
                        历史
                    </Button>
                    <MoveButton />
                    <DeleteButton />
                </EllipsisButton>
            </Flex>
        </Wrapper>

    );
}
